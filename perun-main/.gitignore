# Environment variables
.env

# Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# Usually these files are written by a python script from a template
# before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# PEP 582; used by PDM, PEP 582 proposal
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static analysis results
.pytype/

# Cython debug symbols
cython_debug/

# VSCode specific
.vscode/

# Data files
data/memdir/
data/logs/
# Keep the directories, but ignore contents
data/memdir/*
!data/memdir/.gitkeep
data/logs/*
!data/logs/.gitkeep
data/memdir/tmp/*
!data/memdir/tmp/.gitkeep
data/memdir/new/*
!data/memdir/new/.gitkeep
data/memdir/cur/*
!data/memdir/cur/.gitkeep
data/memdir/cur/index/*
!data/memdir/cur/index/.gitkeep


# Prompt files (if they contain sensitive examples)
# prompts/* # Uncomment if prompts might contain sensitive data

# Temporary files
*.tmp
*.bak
*.swp
*~
