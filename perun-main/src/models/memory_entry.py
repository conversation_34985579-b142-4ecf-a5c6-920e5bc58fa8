from pydantic import BaseModel, <PERSON>, field_validator, ConfigDict
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone # Added timezone
import uuid
from enum import Enum

class MemoryEntryType(str, Enum):
    """Enumeration for different types of memory entries."""
    TRADE = "Trade"
    ANALYSIS = "Analysis"
    ERROR = "Error"
    METRIC = "Metric"
    PORTFOLIO_UPDATE = "PortfolioUpdate"
    SIGNAL = "Signal"
    ORDER_STATUS = "OrderStatus"
    SYSTEM_EVENT = "SystemEvent" # e.g., start, stop, health check
    OPTIMIZATION_RUN = "OptimizationRun"
    PROMPT_UPDATE = "PromptUpdate"
    CONFIG_CHANGE = "ConfigChange"
    MANUAL_NOTE = "ManualNote" # For manual annotations if needed
    RAW_LLM_INTERACTION = "RawLLMInteraction" # Store raw prompt/response

class MemoryMetadata(BaseModel):
    """Optional metadata generated by the AI Organizer."""
    keywords: Optional[List[str]] = None
    summary: Optional[str] = None
    suggested_flags: Optional[List[str]] = None
    relationships: Optional[Dict[str, str]] = None # e.g., {"related_analysis_id": "uuid"}

class MemoryEntry(BaseModel):
    """
     Represents a single entry stored in the Memdir filesystem.
     Each file in Memdir contains one instance of this model serialized as JSON.
     """
    entry_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    entry_type: MemoryEntryType
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc)) # Use timezone-aware now()
    source_service: str # e.g., "AIService", "ExecutionService", "MemoryOrganizer"
    payload: Dict[str, Any] # Flexible payload specific to the entry_type
    metadata: Optional[MemoryMetadata] = None # Populated by MemoryOrganizer

    # Optional: Add validation logic if needed
    # @field_validator('payload')
    # def check_payload_based_on_type(cls, v, values):
    #     entry_type = values.data.get('entry_type')
    #     if entry_type == MemoryEntryType.TRADE and 'symbol' not in v:
    #         raise ValueError('Trade payload must contain "symbol"')
    #     # Add more specific payload validations here based on entry_type
    #     return v

    # Pydantic V2 Configuration (Corrected indentation)
    model_config = ConfigDict(
        use_enum_values=True, # Store enum values as strings
        json_schema_extra={
            "example": {
                "entry_id": "123e4567-e89b-12d3-a456-426614174000",
                "entry_type": "Trade",
                "timestamp": "2024-01-15T10:30:00Z",
                "source_service": "ExecutionService",
                "payload": {
                    "order_id": "98765",
                    "symbol": "AAPL",
                    "side": "buy",
                    "qty": 10,
                    "filled_avg_price": 175.20,
                    "status": "filled",
                    "latency_ms": 150
                },
                "metadata": {
                    "keywords": ["AAPL", "buy", "filled"],
                    "summary": "Executed buy order for 10 shares of AAPL.",
                    "suggested_flags": ["Flag_Trade", "Symbol_AAPL", "Status_Filled"]
                }
            }
        }
    )
