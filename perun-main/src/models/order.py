from pydantic import BaseModel, Field, field_validator, ConfigDict, ValidationInfo # Import ValidationInfo
from typing import Optional, List
from datetime import datetime, timezone
from enum import Enum

class OrderSide(str, Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderTimeInForce(str, Enum):
    DAY = "day"       # Day order
    GTC = "gtc"       # Good 'til canceled
    OPG = "opg"       # Market on open
    CLS = "cls"       # Market on close
    IOC = "ioc"       # Immediate or cancel
    FOK = "fok"       # Fill or kill

class OrderStatus(str, Enum):
    NEW = "new"                     # Order received by broker, not yet active
    ACCEPTED = "accepted"           # Order acknowledged by the exchange
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    DONE_FOR_DAY = "done_for_day"   # Order expired for the day
    CANCELED = "canceled"
    EXPIRED = "expired"
    REPLACED = "replaced"           # Order modified
    PENDING_CANCEL = "pending_cancel"
    PENDING_REPLACE = "pending_replace"
    PENDING_NEW = "pending_new"     # Initial status before acceptance
    REJECTED = "rejected"
    STOPPED = "stopped"             # Stop condition triggered
    SUSPENDED = "suspended"         # Order paused by exchange/broker
    CALCULATED = "calculated"       # Order price calculated by broker (e.g., VWAP)

class Order(BaseModel):
    """Represents a trading order submitted to the brokerage."""
    id: Optional[str] = None # Brokerage-assigned order ID (available after submission)
    client_order_id: str = Field(..., description="Unique ID generated by the trading system")
    symbol: str = Field(..., description="Asset symbol")
    qty: float = Field(..., description="Number of shares/contracts")
    side: OrderSide
    type: OrderType
    time_in_force: OrderTimeInForce
    limit_price: Optional[float] = Field(None, description="Required for limit and stop_limit orders")
    stop_price: Optional[float] = Field(None, description="Required for stop and stop_limit orders")
    trail_price: Optional[float] = Field(None, description="Required for trailing_stop orders (price offset)")
    trail_percent: Optional[float] = Field(None, description="Required for trailing_stop orders (percentage offset)")
    extended_hours: bool = Field(False, description="Whether the order can trade outside regular hours")
    status: OrderStatus = Field(OrderStatus.PENDING_NEW, description="Current status of the order")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Timestamp when the order object was created locally") # Use timezone-aware now()
    submitted_at: Optional[datetime] = Field(None, description="Timestamp when the order was submitted to the broker")
    filled_at: Optional[datetime] = Field(None, description="Timestamp when the order was fully filled")
    expired_at: Optional[datetime] = Field(None, description="Timestamp when the order expired")
    canceled_at: Optional[datetime] = Field(None, description="Timestamp when the order was canceled")
    failed_at: Optional[datetime] = Field(None, description="Timestamp when the order failed")
    replaced_at: Optional[datetime] = Field(None, description="Timestamp when the order was last replaced")
    filled_qty: float = Field(0.0, description="Quantity of the order that has been filled")
    filled_avg_price: Optional[float] = Field(None, description="Average price at which the order was filled")
    legs: Optional[List['Order']] = Field(None, description="For complex orders (e.g., OCO, bracket)") # Recursive type hint using List
    commission: Optional[float] = Field(None, description="Trading commission paid for the order execution")
    notes: Optional[str] = Field(None, description="Any additional notes related to the order")

    # Pydantic V2 Configuration
    model_config = ConfigDict(use_enum_values=True)

    # Pydantic V2 Validators
    @field_validator('limit_price')
    @classmethod
    def check_limit_price(cls, v: Optional[float], info: ValidationInfo): # Use ValidationInfo
        if info.data.get('type') in [OrderType.LIMIT, OrderType.STOP_LIMIT] and v is None:
            raise ValueError('limit_price is required for limit and stop_limit orders')
        return v

    @field_validator('stop_price')
    @classmethod
    def check_stop_price(cls, v: Optional[float], info: ValidationInfo): # Use ValidationInfo
        if info.data.get('type') in [OrderType.STOP, OrderType.STOP_LIMIT] and v is None:
            raise ValueError('stop_price is required for stop and stop_limit orders')
        return v

    # Need to validate trail_price and trail_percent together, potentially using a model validator
    # For simplicity, let's validate them individually first, though a model validator is better
    @field_validator('trail_price', 'trail_percent')
    @classmethod
    def check_trailing_stop_fields(cls, v: Optional[float], info: ValidationInfo): # Use ValidationInfo
        # This validator runs for both fields. We need access to the other field's value.
        # A model validator is generally preferred for cross-field validation in Pydantic v2.
        # Let's keep it simple for now, acknowledging this isn't perfect cross-validation.
        if info.data.get('type') == OrderType.TRAILING_STOP:
             # Basic check: if this field is None, the other must be present (checked separately)
             pass # Individual checks are less effective here.
        return v

    # Example of a model validator for cross-field checks (more robust)
    # from pydantic import model_validator
    # @model_validator(mode='after')
    # def check_trailing_stop_logic(self) -> 'Order':
    #     if self.type == OrderType.TRAILING_STOP:
    #         if self.trail_price is None and self.trail_percent is None:
    #             raise ValueError('Either trail_price or trail_percent is required for trailing_stop orders')
    #         if self.trail_price is not None and self.trail_percent is not None:
    #             raise ValueError('Provide either trail_price or trail_percent for trailing_stop orders, not both')
    #     return self

# Example Usage (can be removed):
# No longer need to import FieldValidationInfo here

# Example Usage (can be removed):
# if __name__ == "__main__":
#     from uuid import uuid4
#     limit_order = Order(
#         client_order_id=f"client-{uuid4()}",
#         symbol="AAPL",
#         qty=10,
#         side=OrderSide.BUY,
#         type=OrderType.LIMIT,
#         time_in_force=OrderTimeInForce.DAY,
#         limit_price=170.50
#     )
#     print("Limit Order:")
#     print(limit_order.model_dump_json(indent=2))

#     market_order = Order(
#         client_order_id=f"client-{uuid4()}",
#         symbol="MSFT",
#         qty=5,
#         side=OrderSide.SELL,
#         type=OrderType.MARKET,
#         time_in_force=OrderTimeInForce.GTC
#     )
#     print("\nMarket Order:")
#     print(market_order.model_dump_json(indent=2))

#     # Example of updating status
#     market_order.status = OrderStatus.ACCEPTED
#     market_order.id = "broker-id-123"
#     market_order.submitted_at = datetime.now(timezone.utc) # Use timezone-aware datetime
#     print("\nUpdated Market Order:")
#     print(market_order.model_dump_json(indent=2))
