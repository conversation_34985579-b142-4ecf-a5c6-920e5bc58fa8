from pydantic import BaseModel, Field, field_validator, ConfigDict, ValidationInfo # Import ValidationInfo
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from enum import Enum

class SignalAction(str, Enum):
    """Action recommended by the trading signal."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_LONG = "close_long" # Explicitly close an existing long position
    CLOSE_SHORT = "close_short" # Explicitly close an existing short position

class SignalSource(str, Enum):
    """Source of the trading signal."""
    AI_ANALYSIS = "ai_analysis"
    TECHNICAL_INDICATOR = "technical_indicator" # If using traditional indicators
    MANUAL_OVERRIDE = "manual_override"
    RISK_MANAGEMENT = "risk_management" # e.g., stop-loss triggered signal

class TradingSignal(BaseModel):
    """
    Represents a trading signal generated by the system (typically AI Service).
     This is the input for the Execution Service.
     """
    signal_id: str = Field(..., description="Unique identifier for this signal instance")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Timestamp when the signal was generated") # Use timezone-aware now()
    symbol: str = Field(..., description="Asset symbol the signal pertains to")
    action: SignalAction = Field(..., description="Recommended trading action")
    source: SignalSource = Field(SignalSource.AI_ANALYSIS, description="Origin of the signal")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence level of the signal (0.0 to 1.0)")
    target_price: Optional[float] = Field(None, description="Suggested target price for limit orders or profit taking")
    stop_loss_price: Optional[float] = Field(None, description="Suggested stop-loss price")
    rationale: Optional[str] = Field(None, description="Brief explanation or justification for the signal (e.g., from LLM)")
    originating_memory_id: Optional[str] = Field(None, description="ID of the MemoryEntry (e.g., Analysis) that led to this signal")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Any additional metadata associated with the signal")
 
     # Pydantic V2 Configuration
     # model_config = ConfigDict(use_enum_values=True) # Temporarily commented out for debugging AttributeError
 
     # Pydantic V2 Validator (Example - can be expanded)
    @field_validator('action')
    @classmethod
    def check_action_specific_fields(cls, v: SignalAction, info: ValidationInfo): # Use ValidationInfo
        """Basic validation based on action type."""
        # Example: Could add validation like confidence is required for BUY/SELL
        # if v in [SignalAction.BUY, SignalAction.SELL] and info.data.get('confidence') is None:
        #     raise ValueError("Confidence score is required for BUY/SELL signals.")
        return v

# Example Usage (can be removed):
# if __name__ == "__main__":
#     import uuid
#     buy_signal = TradingSignal(
#         signal_id=str(uuid.uuid4()),
#         symbol="NVDA",
#         action=SignalAction.BUY,
#         confidence=0.85,
#         target_price=950.0,
#         stop_loss_price=880.0,
#         rationale="Strong bullish indicators and positive news sentiment.",
#         originating_memory_id=str(uuid.uuid4()) # ID of the analysis memory entry
#     )
#     print("Buy Signal:")
#     print(buy_signal.model_dump_json(indent=2))

#     hold_signal = TradingSignal(
#         signal_id=str(uuid.uuid4()),
#         symbol="AAPL",
#         action=SignalAction.HOLD,
#         rationale="Market conditions neutral, waiting for clearer trend.",
#         originating_memory_id=str(uuid.uuid4())
#     )
#     print("\nHold Signal:")
#     print(hold_signal.model_dump_json(indent=2))
