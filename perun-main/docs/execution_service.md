# Execution Service

## Overview

The Execution Service, implemented in the `ExecutionManager` class (`src/services/execution_service/manager.py`), is responsible for translating trading signals generated by the AI Service into actual orders placed with the brokerage. It manages the lifecycle of orders and ensures trades align with predefined risk parameters.

## Responsibilities

*   **Signal Processing:** Receives `Signal` objects from the `OrchestrationDaemon`.
*   **Risk Management & Sizing:** Calculates the appropriate order size (quantity) based on the signal, current portfolio value, available buying power, and configured risk parameters (e.g., `TRADE_RISK_PER_TRADE`). This is a critical step to prevent excessive losses.
*   **Order Generation:** Creates structured `Order` objects based on the signal and calculated size, specifying details like symbol, side (buy/sell), quantity, order type (market, limit), and potentially time-in-force.
*   **Brokerage Interaction:** Uses the `BrokerageInterface` to:
    *   Place the generated orders with the brokerage.
    *   Monitor the status of open orders (e.g., pending, filled, cancelled).
    *   Fetch current portfolio information (positions, cash balance) needed for sizing and validation.
    *   Cancel orders if necessary (e.g., based on time limits or new signals).
*   **Portfolio Update (Coordination):** While the `BrokerageInterface` fetches portfolio data, the Execution Service might be responsible for triggering updates or ensuring the system's internal representation of the portfolio stays synchronized after trades are executed.
*   **Error Handling:** Manages errors related to order placement or monitoring (e.g., insufficient funds, invalid symbol, API errors, rejected orders).

## Workflow

1.  **Receive Signal:** The `OrchestrationDaemon` passes a `Signal` object to the `ExecutionManager` (e.g., via an `execute_signal` method).
2.  **Fetch Portfolio Data:** The manager uses the `BrokerageInterface` to get the latest portfolio status (cash, existing positions, buying power).
3.  **Validate Signal:** Checks if the signal is actionable (e.g., sufficient buying power for a buy, holding the asset for a sell).
4.  **Calculate Order Size:** Determines the number of shares/contracts to trade based on the risk configuration (`TRADE_RISK_PER_TRADE`), account value, and potentially stop-loss levels implied by the signal or strategy.
5.  **Create Order Object:** Constructs the `Order` object with all necessary details.
6.  **Place Order:** Uses the `BrokerageInterface`'s `submit_order` method to send the order to the brokerage.
7.  **Monitor Order Status:** Periodically checks the status of the submitted order using the `BrokerageInterface` (e.g., `get_order_status`). This might happen immediately or be handled by a separate monitoring loop within the service or daemon.
8.  **Record Execution:** Once an order is confirmed filled, logs the execution details and updates the internal state or notifies the `OrchestrationDaemon`.
9.  **Update Memory:** Ensures the trade execution details are recorded by the `MemoryService`.

## Configuration

The Execution Service relies on:

*   **Brokerage API Credentials:** (`APCA_API_KEY_ID`, `APCA_API_SECRET_KEY`, `APCA_API_BASE_URL`) defined in `.env`.
*   **Risk Parameters:** (`TRADE_RISK_PER_TRADE`) defined in `.env`.
*   **Trading Symbols:** (`TRADING_SYMBOLS`) to validate signals against.

## Key Interactions

*   **`OrchestrationDaemon`:** Receives signals from the daemon and reports execution status back.
*   **`BrokerageInterface`:** Heavily relies on this interface for all interactions with the trading platform (placing orders, getting status, fetching portfolio).
*   **`Signal` Model:** Consumes `Signal` objects as input.
*   **`Order` Model:** Creates and manages `Order` objects.
*   **`Portfolio` Model:** Reads portfolio data to make decisions.
*   **`MemoryService`:** Stores records of placed and executed orders.
*   **`config.py`:** Reads brokerage credentials and risk settings.
*   **`logger.py`:** Logs order placement attempts, fills, and errors.
